.user-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px;
  max-width: 300px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.user-photo {
  margin-bottom: 15px;
}

.user-photo img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #f0f0f0;
}

.user-info {
  color: #333;
}

.user-name {
  margin: 0 0 10px 0;
  font-size: 1.5em;
  font-weight: 600;
  color: #2c3e50;
}

.user-age {
  margin: 0;
  font-size: 1.1em;
  color: #7f8c8d;
  font-weight: 500;
}
